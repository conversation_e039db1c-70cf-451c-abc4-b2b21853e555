package net.lingyue.ly.admin.autoRealy.interfaceTask.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingyue.ly.admin.autoRealy.dataSourceManage.dao.DataSourceDao;
import net.lingyue.ly.admin.autoRealy.dataSourceManage.dao.DbDriverDao;
import net.lingyue.ly.admin.autoRealy.dataSourceManage.domain.AutoDataSource;
import net.lingyue.ly.admin.autoRealy.dataSourceManage.domain.DbDriver;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.InterfaceConfigEntity;

import net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.vo.InterfaceTestResultVO;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.manager.InterfaceConfigManager;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.service.InterfaceConfigService;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.service.InterfaceInvokeService;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.service.ResponseProcessorService;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.service.EncryptionSignatureService;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.service.TemplateEngineService;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.service.ParamMappingService;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.manager.InterfaceParamMappingManager;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.InterfaceParamMappingEntity;
import net.lingyue.ly.admin.autoRealy.interfaceConfig.domain.vo.SqlQueryResultVO;
import net.lingyue.ly.admin.autoRealy.pushRecord.dao.PushConfigDao;
import net.lingyue.ly.admin.autoRealy.pushRecord.domain.entity.PushConfigEntity;
import net.lingyue.ly.admin.autoRealy.pushRecord.service.PushResponseParseService;
import net.lingyue.ly.admin.autoRealy.interfaceTask.dao.InterfaceTaskDao;
import net.lingyue.ly.admin.autoRealy.interfaceTask.dao.InterfaceTaskLogDao;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.entity.InterfaceTaskEntity;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.entity.InterfaceTaskLogEntity;
import net.lingyue.ly.admin.autoRealy.pushRecord.service.BatchProcessingService;
import net.lingyue.ly.admin.autoRealy.pushRecord.service.PushRecordService;
import net.lingyue.ly.admin.autoRealy.pushRecord.service.BreakpointService;

import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.form.InterfaceTaskAddForm;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.form.InterfaceTaskQueryForm;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.form.InterfaceTaskUpdateForm;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.vo.InterfaceTaskVO;
import net.lingyue.ly.admin.autoRealy.interfaceTask.domain.vo.InterfaceTaskLogVO;
import net.lingyue.ly.base.common.domain.PageResult;
import net.lingyue.ly.base.common.domain.ResponseDTO;
import net.lingyue.ly.base.common.domain.RequestUser;
import net.lingyue.ly.base.common.exception.BusinessException;
import net.lingyue.ly.base.common.util.SmartPageUtil;
import net.lingyue.ly.base.common.util.SmartRequestUtil;
import net.lingyue.ly.base.module.support.job.api.domain.SmartJobAddForm;
import net.lingyue.ly.base.module.support.job.api.domain.SmartJobUpdateForm;
import net.lingyue.ly.base.module.support.job.constant.SmartJobTriggerTypeEnum;
import net.lingyue.ly.base.module.support.job.service.InterfaceJobService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.LinkedHashSet;

/**
 * 接口任务服务类
 *
 * <AUTHOR>
 * @Date 2024-07-06
 * @Copyright <a href="https://1024lab.net">1024创新实验室</a>
 */
@Slf4j
@Service
public class InterfaceTaskService {

    @Resource
    private InterfaceTaskDao interfaceTaskDao;

    @Resource
    private InterfaceConfigManager interfaceConfigManager;

    @Resource
    private DataSourceDao dataSourceDao;

    @Resource
    private InterfaceInvokeService interfaceInvokeService;

    @Resource
    private InterfaceJobService interfaceJobService;

    @Resource
    private DbDriverDao dbDriverDao;

    @Resource
    private InterfaceConfigService interfaceConfigService;

    @Resource
    private InterfaceTaskLogDao interfaceTaskLogDao;

    @Resource
    private ApplicationContext applicationContext;

    @Resource
    private EncryptionSignatureService encryptionSignatureService;

    @Resource
    private TemplateEngineService templateEngineService;

    @Resource
    private ObjectMapper objectMapper;

    @Resource
    private PushResponseParseService pushResponseParseService;

    @Resource
    private ResponseProcessorService responseProcessorService;

    @Resource
    private PushRecordService pushRecordService;



    @Resource
    private BatchProcessingService batchProcessingService;

    @Resource
    private ParamMappingService paramMappingService;

    @Resource
    private InterfaceParamMappingManager interfaceParamMappingManager;
    @Resource
    private PushConfigDao pushConfigDao;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private BreakpointService breakpointService;

    /**
     * 分页查询接口任务
     *
     * @param queryForm 查询条件
     * @return 分页结果
     */
    public ResponseDTO<PageResult<InterfaceTaskVO>> queryPage(InterfaceTaskQueryForm queryForm) {
        Page<?> page = SmartPageUtil.convert2PageQuery(queryForm);
        List<InterfaceTaskVO> list = interfaceTaskDao.queryPage(page, queryForm);

        // 计算下次执行时间
        for (InterfaceTaskVO vo : list) {
            calculateNextExecuteTime(vo);
        }

        PageResult<InterfaceTaskVO> pageResult = SmartPageUtil.convert2PageResult(page, list);
        return ResponseDTO.ok(pageResult);
    }

    /**
     * 获取任务详情
     *
     * @param id 任务ID
     * @return 任务详情
     */
    public ResponseDTO<InterfaceTaskVO> getById(Long id) {
        InterfaceTaskEntity entity = interfaceTaskDao.selectById(id);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("任务不存在");
        }

        InterfaceTaskVO vo = new InterfaceTaskVO();
        BeanUtils.copyProperties(entity, vo);


        // 计算下次执行时间
        calculateNextExecuteTime(vo);

        return ResponseDTO.ok(vo);
    }

    /**
     * 添加接口任务
     *
     * @param form 添加表单
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> add(InterfaceTaskAddForm form) {
        // 验证接口是否存在
        InterfaceConfigEntity interfaceConfig = interfaceConfigManager.getById(form.getInterfaceId());
        if (interfaceConfig == null) {
            return ResponseDTO.userErrorParam("接口配置不存在");
        }

        // 校验触发类型
        if (!SmartJobTriggerTypeEnum.CRON.getValue().equals(form.getTriggerType())
                && !SmartJobTriggerTypeEnum.FIXED_DELAY.getValue().equals(form.getTriggerType())) {
            return ResponseDTO.userErrorParam("触发类型错误");
        }

        // 校验触发值
        if (SmartJobTriggerTypeEnum.CRON.getValue().equals(form.getTriggerType())) {
            if (!isCronExpressionValid(form.getTriggerValue())) {
                return ResponseDTO.userErrorParam("Cron表达式格式错误");
            }
        } else if (SmartJobTriggerTypeEnum.FIXED_DELAY.getValue().equals(form.getTriggerType())) {
            try {
                int seconds = Integer.parseInt(form.getTriggerValue());
                if (seconds <= 0) {
                    return ResponseDTO.userErrorParam("固定间隔时间必须大于0");
                }
            } catch (NumberFormatException e) {
                return ResponseDTO.userErrorParam("固定间隔时间必须是整数");
            }
        }

        // 设置任务实体
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        InterfaceTaskEntity entity = new InterfaceTaskEntity();
        BeanUtils.copyProperties(form, entity);
        entity.setInterfaceName(interfaceConfig.getInterfaceName());
        entity.setSystemCode(interfaceConfig.getSystemCode());
        entity.setLastExecuteStatus(0);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setCreateUserId(requestUser.getUserId());
        entity.setUpdateUserId(requestUser.getUserId());
        entity.setDeletedFlag(false);

        // 保存任务
        interfaceTaskDao.insert(entity);

        // 如果启用，创建定时任务
        if (form.getEnabledFlag()) {
            createOrUpdateJob(entity);
        }

        return ResponseDTO.ok();
    }

    /**
     * 更新接口任务
     *
     * @param form 更新表单
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> update(InterfaceTaskUpdateForm form) {
        // 检查任务是否存在
        InterfaceTaskEntity existEntity = interfaceTaskDao.selectById(form.getId());
        if (existEntity == null || existEntity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("任务不存在");
        }

        // 验证接口是否存在
        InterfaceConfigEntity interfaceConfig = interfaceConfigManager.getById(form.getInterfaceId());
        if (interfaceConfig == null) {
            return ResponseDTO.userErrorParam("接口配置不存在");
        }

        // 校验触发类型
        if (!SmartJobTriggerTypeEnum.CRON.getValue().equals(form.getTriggerType())
                && !SmartJobTriggerTypeEnum.FIXED_DELAY.getValue().equals(form.getTriggerType())) {
            return ResponseDTO.userErrorParam("触发类型错误");
        }

        // 校验触发值
        if (SmartJobTriggerTypeEnum.CRON.getValue().equals(form.getTriggerType())) {
            if (!isCronExpressionValid(form.getTriggerValue())) {
                return ResponseDTO.userErrorParam("Cron表达式格式错误");
            }
        } else if (SmartJobTriggerTypeEnum.FIXED_DELAY.getValue().equals(form.getTriggerType())) {
            try {
                int seconds = Integer.parseInt(form.getTriggerValue());
                if (seconds <= 0) {
                    return ResponseDTO.userErrorParam("固定间隔时间必须大于0");
                }
            } catch (NumberFormatException e) {
                return ResponseDTO.userErrorParam("固定间隔时间必须是整数");
            }
        }

        // 设置任务实体
        RequestUser requestUser = SmartRequestUtil.getRequestUser();
        InterfaceTaskEntity entity = new InterfaceTaskEntity();
        BeanUtils.copyProperties(form, entity);
        entity.setInterfaceName(interfaceConfig.getInterfaceName());
        entity.setSystemCode(interfaceConfig.getSystemCode());
        entity.setUpdateTime(LocalDateTime.now());
        entity.setUpdateUserId(requestUser.getUserId());

        // 更新任务
        interfaceTaskDao.updateById(entity);

        // 如果启用，更新定时任务；如果禁用，删除定时任务
        if (form.getEnabledFlag()) {
            createOrUpdateJob(entity);
        } else {
            removeJob(entity.getId());
        }

        return ResponseDTO.ok();
    }

    /**
     * 删除接口任务
     *
     * @param id 任务ID
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> delete(Long id) {
        InterfaceTaskEntity entity = interfaceTaskDao.selectById(id);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("任务不存在");
        }

        // 执行假删除
        interfaceTaskDao.updateDeletedFlag(id, true);

        // 删除相关定时任务
        removeJob(id);

        // 清理推送记录
        try {
            pushRecordService.cleanupPushRecords(id);
            log.info("任务[{}]的推送记录已清理", id);
        } catch (Exception e) {
            log.warn("清理任务[{}]推送记录失败: {}", id, e.getMessage());
        }

        return ResponseDTO.ok();
    }

    /**
     * 更新任务状态
     *
     * @param id 任务ID
     * @param status 状态
     * @return 操作结果
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseDTO<String> updateStatus(Long id, Boolean status) {
        InterfaceTaskEntity entity = interfaceTaskDao.selectById(id);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("任务不存在");
        }

        // 更新状态
        InterfaceTaskEntity updateEntity = new InterfaceTaskEntity();
        updateEntity.setId(id);
        updateEntity.setEnabledFlag(status);
        updateEntity.setUpdateTime(LocalDateTime.now());
        updateEntity.setUpdateUserId(SmartRequestUtil.getRequestUser().getUserId());
        interfaceTaskDao.updateById(updateEntity);

        // 根据状态创建或删除任务
        if (status) {
            entity.setEnabledFlag(true);
            createOrUpdateJob(entity);
        } else {
            removeJob(id);
        }

        return ResponseDTO.ok();
    }

    /**
     * 手动执行任务
     *
     * @param id 任务ID
     * @return 执行结果
     */
    public ResponseDTO<InterfaceTaskLogVO> executeTask(Long id) {
        InterfaceTaskEntity entity = interfaceTaskDao.selectById(id);
        if (entity == null || entity.getDeletedFlag()) {
            return ResponseDTO.userErrorParam("任务不存在");
        }

        try {
            // 执行任务（executeInterfaceTask方法内部会保存日志）
            InterfaceTaskLogVO logVO = executeInterfaceTask(entity);

            // 更新最后执行时间和状态
            InterfaceTaskEntity updateEntity = new InterfaceTaskEntity();
            updateEntity.setId(entity.getId());
            updateEntity.setLastExecuteTime(LocalDateTime.now());
            updateEntity.setLastExecuteStatus(logVO.getSuccess() ? 1 : 0);
            interfaceTaskDao.updateById(updateEntity);

            return ResponseDTO.ok(logVO);
        } catch (Exception e) {
            log.error("手动执行任务失败", e);
            // 创建日志对象（此时executeInterfaceTask方法可能未被执行）
            InterfaceTaskLogVO logVO = new InterfaceTaskLogVO();
            logVO.setTaskId(id);
            logVO.setTaskName(entity.getTaskName());
            logVO.setSuccess(false);
            logVO.setExecuteResult("执行失败：" + e.getMessage());
            logVO.setExecuteStartTime(LocalDateTime.now());
            logVO.setExecuteEndTime(LocalDateTime.now());
            logVO.setExecuteTimeMillis(0L);

            // 保存失败日志（因为executeInterfaceTask可能未执行，所以这里需要保存）
            saveTaskLog(logVO);

            return ResponseDTO.ok(logVO);
        }
    }

    /**
     * 定时任务执行方法（由Scheduler调用）
     *
     * @param taskId 任务ID
     * @param param 参数
     * @return 执行结果
     */
    public String executeScheduledTask(Integer taskId, String param) {
        InterfaceTaskEntity entity = interfaceTaskDao.selectById(Long.valueOf(taskId));
        if (entity == null || entity.getDeletedFlag() || !entity.getEnabledFlag()) {
            return "任务不存在或已禁用";
        }

        try {
            // 执行任务（executeInterfaceTask方法内部会保存日志）
            InterfaceTaskLogVO logVO = executeInterfaceTask(entity);

            // 更新最后执行时间和状态
            InterfaceTaskEntity updateEntity = new InterfaceTaskEntity();
            updateEntity.setId(entity.getId());
            updateEntity.setLastExecuteTime(LocalDateTime.now());
            updateEntity.setLastExecuteStatus(logVO.getSuccess() ? 1 : 0);
            interfaceTaskDao.updateById(updateEntity);

            return logVO.getExecuteResult();
        } catch (Exception e) {
            log.error("定时执行任务失败", e);

            // 创建日志对象（此时executeInterfaceTask方法可能未被执行）
            InterfaceTaskLogVO logVO = new InterfaceTaskLogVO();
            logVO.setTaskId(entity.getId());
            logVO.setTaskName(entity.getTaskName());
            logVO.setSuccess(false);
            logVO.setExecuteResult("执行失败：" + e.getMessage());
            logVO.setExecuteStartTime(LocalDateTime.now());
            logVO.setExecuteEndTime(LocalDateTime.now());
            logVO.setExecuteTimeMillis(0L);

            // 保存失败日志（因为executeInterfaceTask可能未执行，所以这里需要保存）
            saveTaskLog(logVO);

            // 更新最后执行时间和状态
            InterfaceTaskEntity updateEntity = new InterfaceTaskEntity();
            updateEntity.setId(entity.getId());
            updateEntity.setLastExecuteTime(LocalDateTime.now());
            updateEntity.setLastExecuteStatus(0);
            interfaceTaskDao.updateById(updateEntity);

            return "执行失败：" + e.getMessage();
        }
    }

    /**
     * 执行接口任务
     *
     * @param entity 任务实体
     * @return 日志记录
     */
    private InterfaceTaskLogVO executeInterfaceTask(InterfaceTaskEntity entity) {
        long startTime = System.currentTimeMillis();
        InterfaceTaskLogVO logVO = new InterfaceTaskLogVO();
        logVO.setTaskId(entity.getId());
        logVO.setTaskName(entity.getTaskName());
        logVO.setExecuteStartTime(LocalDateTime.now());

        // 批次处理结果，用于后续更新进度
        BatchProcessingService.BatchProcessingResult batchResult = null;
        // 上报数据详情（加密前的JSON数据）
        String uploadDataDetail = null;
        // 断点信息，用于完整保存
        BreakpointService.BreakpointInfo breakpoint = null;

        try {
            // 获取接口配置
            InterfaceConfigEntity interfaceConfig = interfaceConfigManager.getById(entity.getInterfaceId());
            if (interfaceConfig == null) {
                throw new IllegalArgumentException("接口配置不存在");
            }
            // 获取推送配置
            PushConfigEntity pushConfig = pushConfigDao.selectByTaskId(entity.getId());
            if (pushConfig == null) {
                throw new IllegalArgumentException("推送配置不存在");
            }
            List<Map<String, Object>> tableData = null;

            if (interfaceConfig.getDataSourceId() != null) {
                // 获取数据源
                AutoDataSource dataSource = dataSourceDao.getById(interfaceConfig.getDataSourceId());
                if (dataSource == null) {
                    throw new IllegalArgumentException("数据源不存在");
                }
                // 获取数据库驱动
                DbDriver dbDriver = dbDriverDao.getById(dataSource.getDriverId());
                if (dbDriver == null) {
                    throw new IllegalArgumentException("数据库驱动不存在");
                }

                // 🔥 正确设计：在executeInterfaceTask中获取和计算完整的断点信息
                breakpoint = breakpointService.getCurrentBreakpoint(entity.getId());
                // 🔥 正确设计：在这里计算时间窗口，确保推送成功后能保存完整信息
                calculateCurrentTimeWindow(breakpoint, pushConfig);

                // 直接使用原始SQL
                String processedSql = interfaceConfig.getSqlQuery();

                // 🔥 正确设计：将完整的断点信息传给BatchProcessingService
                batchResult = batchProcessingService.processBatchDataWithBreakpoint(entity, pushConfig, dataSource, dbDriver, processedSql, breakpoint);

                if (!batchResult.isSuccess()) {
                    throw new RuntimeException("分批处理失败: " + batchResult.getErrorMessage());
                }

                log.info("任务[{}]分批处理完成，批次[{}]，总处理{}条，成功{}条，失败{}条",
                        entity.getId(), batchResult.getBatchNo(),
                        batchResult.getTotalProcessed(), batchResult.getTotalSuccess(), batchResult.getTotalFailed());

                // 检查是否有数据需要推送
                if (batchResult.getTotalSuccess() == 0) {
                    logVO.setSuccess(true);
                    logVO.setExecuteResult("无新数据需要推送（未执行数据组装）");
                    logVO.setRequestBody("无数据");
                    logVO.setResponseBody("无新数据需要推送");
                    return logVO;
                }

                // 获取过滤后的数据用于接口推送
                List<Map<String, Object>> filteredData = batchResult.getFilteredData();
                if (filteredData != null && !filteredData.isEmpty()) {
                    tableData = filteredData;
                    log.info("任务[{}]准备推送{}条过滤后的数据", entity.getId(), filteredData.size());
                } else {
                    // 🔥 关键修复：没有数据需要推送时，直接返回成功结果，不调用接口
                    log.info("任务[{}]无数据需要推送（全部已处理），跳过接口调用，最大ID已更新", entity.getId());

                    logVO.setSuccess(true);
                    logVO.setExecuteEndTime(LocalDateTime.now());
                    logVO.setResponseBody("无数据需要推送，跳过接口调用");

                    // 构建推送结果信息
                    Map<String, Object> pushInfo = new HashMap<>();
                    pushInfo.put("batchNo", batchResult.getBatchNo());
                    pushInfo.put("totalProcessed", batchResult.getTotalProcessed());
                    pushInfo.put("totalSuccess", 0);
                    pushInfo.put("totalFailed", 0);
                    pushInfo.put("message", "无数据需要推送，最大ID已更新");

                    // 记录上报数据详情
                    uploadDataDetail = JSONObject.toJSONString(pushInfo);

                    return logVO;
                }
            }

            // 第一步：数据组装 - 使用入参模板处理原始数据
            Object assembledData = assembleRequestDataForTask(interfaceConfig, tableData);
            log.info("任务调度：数据组装完成，数据类型: {}", assembledData.getClass().getSimpleName());
            // 记录上报数据详情（加密前的JSON数据）
            uploadDataDetail = buildUploadDataSummary(assembledData, 50, 200_000);
            // 第二步：加密签名处理（如果需要）
            Object finalRequestBody = assembledData;
            if (encryptionSignatureService.needsEncryptionOrSignature(interfaceConfig)) {
                log.info("任务调度：开始进行加密签名处理");

                EncryptionSignatureService.EncryptionSignatureResult encryptResult =
                    encryptionSignatureService.processEncryptionAndSignature(assembledData, interfaceConfig);

                if (encryptResult.isSuccess()) {
                    finalRequestBody = encryptResult.getFinalRequestBody();
                    log.info("任务调度：加密签名处理成功，使用处理后的请求体");
                } else {
                    log.error("任务调度：加密签名处理失败: {}", encryptResult.getErrorMessage());
                    // 继续使用组装后的数据，不因加密失败而中断任务
                }
            } else {
                log.info("任务调度：接口未配置加密和签名，直接使用组装后的数据");
            }

            // 第三步：调用接口（使用已处理的数据，避免重复加密签名）
            InterfaceTestResultVO result;
            if (encryptionSignatureService.needsEncryptionOrSignature(interfaceConfig)) {
                // 如果配置了加密签名，使用简化版本的接口调用，避免重复处理
                result = interfaceInvokeService.invokeInterfaceWithProcessedData(interfaceConfig, finalRequestBody);
            } else {
                // 如果没有配置加密签名，使用原有的接口调用方法
                result = interfaceInvokeService.invokeInterface(interfaceConfig, finalRequestBody);
            }

            // 设置日志结果
            boolean isSuccess = result.getStatusCode() != null && result.getStatusCode() >= 200 && result.getStatusCode() < 300;
            logVO.setSuccess(isSuccess);

            // 记录推送结果到推送记录表 - 使用精确解析
            if (tableData != null) {

                if (isSuccess && result.getResponseBody() != null) {
                    // 🔥 修复：接口调用成功，只处理失败的数据
                    String responseBodyStr = result.getResponseBody().toString();
                    pushResponseParseService.parseAndHandleFailedDataOnly(
                        interfaceConfig, entity.getId(), responseBodyStr, pushConfig.getTargetDataIdField(), tableData);

                    // 🎯 关键：接口调用成功，更新批次进度并保存断点信息
                    if (batchResult.getBatchEntity() != null) {
                        batchProcessingService.updateBatchProgressAfterSuccess(
                            batchResult.getBatchEntity(), tableData, pushConfig);

                        // 🔥 正确设计：推送成功后保存完整的断点信息（包括时间窗口）
                        if (batchResult.getCalculatedBreakpoint() != null) {
                            // 合并时间窗口信息到数据断点信息中
                            BreakpointService.BreakpointInfo finalBreakpoint = batchResult.getCalculatedBreakpoint();
                            finalBreakpoint.setCurrentWindowStart(breakpoint.getCurrentWindowStart());
                            finalBreakpoint.setCurrentWindowEnd(breakpoint.getCurrentWindowEnd());


                            // 🔥 正确逻辑：窗口完成状态已在BatchProcessingService中根据原始查询数据量正确计算
                            // 直接使用BatchProcessingService计算的结果，不需要重新计算
                            // finalBreakpoint中的windowCompleted已经是正确的值

                            breakpointService.saveBreakpoint(entity.getId(), finalBreakpoint, batchResult.getBatchEntity());
                            log.info("任务[{}]推送成功，完整断点信息已保存: 时间={}, ID={}, 窗口={} ~ {}, 完成={} (原始查询:{}/{})",
                                    entity.getId(),
                                    finalBreakpoint.getLastTime(),
                                    finalBreakpoint.getLastId(),
                                    finalBreakpoint.getCurrentWindowStart(),
                                    finalBreakpoint.getCurrentWindowEnd(),
                                    finalBreakpoint.isWindowCompleted(),
                                    batchResult.getOriginalQueryCount(),
                                    pushConfig.getBatchSize());
                        }
                    }

                    log.info("任务[{}]接口调用成功，已更新批次进度并保存断点，数据量: {}", entity.getId(), tableData.size());
                } else {
                    // 🔥 修复：接口调用失败，需要清理Redis中的预标记数据
                    String baseErrorMessage = "接口调用失败 - HTTP状态码: " + result.getStatusCode();
                    String responseBody = result.getResponseBody() != null ? result.getResponseBody().toString() : "";
                    if (StringUtils.isNotBlank(responseBody)) {
                        baseErrorMessage += ", 响应内容: " + responseBody;
                    }
                    String batchNo = batchResult.getBatchNo();

                    // 从Redis中移除所有预标记的数据（因为接口调用失败，所有数据都应视为失败）
                    List<String> allDataIds = new ArrayList<>();
                    for (Map<String, Object> dataRow : tableData) {
                        Object dataIdObj = dataRow.get(pushConfig.getTargetDataIdField());
                        if (dataIdObj != null) {
                            allDataIds.add(dataIdObj.toString());
                        }
                    }

                    if (!allDataIds.isEmpty()) {
                        pushRecordService.removeFailedDataFromRedis(entity.getId(), allDataIds);
                        log.info("🔥 任务[{}]接口调用失败，已从Redis中移除{}条预标记数据", entity.getId(), allDataIds.size());
                    }

                    // 批量记录失败状态到数据库，避免循环多次写库
                    pushRecordService.batchRecordPushResultOptimized(entity.getId(), tableData, false, baseErrorMessage, batchNo);

                    // 🎯 关键：接口调用失败，标记批次失败（保持上次成功的lastProcessedId）
                    if (batchResult.getBatchEntity() != null) {
                        batchProcessingService.markBatchFailedAfterInterfaceError(
                            batchResult.getBatchEntity(), baseErrorMessage);
                    }

                    log.warn("任务[{}]接口调用失败，已标记批次失败并清理Redis预标记，保持上次成功进度，数据量: {}", entity.getId(), tableData.size());
                }
            }

            // 处理请求体和响应体，确保转换为字符串
            Object requestBody = result.getRequestBody();
            logVO.setRequestBody(requestBody != null ? requestBody.toString() : null);

            Object responseBody = result.getResponseBody();
            logVO.setResponseBody(responseBody != null ? responseBody.toString() : null);
            // 设置上报数据详情（记录加密前的JSON数据）
            logVO.setExecuteResult(uploadDataDetail);

            logVO.setResponseHeaders(result.getResponseHeaders());
            logVO.setStatusCode(result.getStatusCode());

            // 处理接口响应 - 使用ResponseProcessorService
            if (StringUtils.isNotBlank(interfaceConfig.getResponseProcessorClass())) {
                try {
                    log.info("开始使用处理器处理接口响应: {}", interfaceConfig.getResponseProcessorClass());

                    // 执行处理
                    Object processedResult = responseProcessorService.processResponse(result, interfaceConfig);

                    // 记录处理器处理结果到日志，但不覆盖executeResult字段
                    if (processedResult != null) {
                        log.info("处理器处理结果: {}", processedResult);
                        // 注意：不再覆盖executeResult，保持上报数据详情
                    }
                } catch (Exception e) {
                    log.error("执行响应处理器失败", e);
                    // 响应处理器失败时，仍然保持上报数据详情
                }
            }
        } catch (Exception e) {
            logVO.setSuccess(false);
            // 只有在没有上报数据详情时才设置异常信息，否则保持上报数据详情
            if (uploadDataDetail == null) {
                logVO.setExecuteResult("执行失败：" + e.getMessage());
            }
        } finally {
            logVO.setExecuteEndTime(LocalDateTime.now());
            logVO.setExecuteTimeMillis(System.currentTimeMillis() - startTime);

            // 保存任务执行日志
            saveTaskLog(logVO);
        }

        return logVO;
    }

    /**
     * 计算当前时间窗口
     *
     * 关键逻辑：
     * 1. 如果有断点时间，下一窗口从断点时间开始，而不是配置的起始时间
     * 2. 确保窗口计算的连续性和正确性
     */
    private void calculateCurrentTimeWindow(BreakpointService.BreakpointInfo breakpoint, PushConfigEntity pushConfig) {
        // 获取时间窗口大小（小时），默认1小时
        int timeWindowHours = pushConfig.getTimeWindowHours() != null ? pushConfig.getTimeWindowHours() : 1;
        int timeWindowMinutes = timeWindowHours * 60; // 转换为分钟用于计算

        LocalDateTime lastTime;
        // 优先使用断点时间作为数据开始时间
        if (breakpoint.getLastTime() != null) {
            // 基于上次处理时间计算
            lastTime = breakpoint.getLastTime(); // 从断点时间开始，而不是配置的起始时间
            log.info("基于断点时间计算窗口起始时间: {}", lastTime);
        } else {
            // 首次执行，使用配置的起始时间
            lastTime = pushConfig.getStartTime();
            breakpoint.setLastTime(lastTime);
            breakpoint.setCurrentWindowStart(lastTime);
            breakpoint.setCurrentWindowEnd(lastTime.plusMinutes(timeWindowMinutes));
            log.info("首次执行，使用配置起始时间: {}", lastTime);
            return;
        }
        if (breakpoint.isWindowCompleted()) {
            // 当前窗口完成，开始时间使用上次处理时间，结束时间增加时间窗口大小
            LocalDateTime localDateTime = breakpoint.getCurrentWindowEnd().plusMinutes(timeWindowMinutes);
            // 检查下一窗口是否超过当前时间
            LocalDateTime now = LocalDateTime.now();
            if (localDateTime.isAfter(now)) {
                // 如果下一窗口结束时间超过当前时间，调整为当前时间
                localDateTime = now;
                log.info("下一窗口结束时间调整为当前时间: {}", localDateTime);
            }
            breakpoint.setCurrentWindowStart(breakpoint.getCurrentWindowEnd());
            breakpoint.setCurrentWindowEnd(localDateTime);
            log.info("新的时间窗口: {} ~ {}", breakpoint.getCurrentWindowStart(), breakpoint.getCurrentWindowEnd());
        }
    }

    /**
     * 为任务调度组装请求数据
     *
     * @param interfaceConfig 接口配置
     * @param rawData 原始数据
     * @return 组装后的数据
     */
    private Object assembleRequestDataForTask(InterfaceConfigEntity interfaceConfig, Object rawData) {
        try {
            // 如果没有配置入参模板，直接返回原始数据
            if (StringUtils.isBlank(interfaceConfig.getInputDataTemplate())) {
                log.info("任务调度：未配置入参模板，直接使用原始数据");
                return rawData;
            }

            log.info("任务调度：开始使用入参模板组装数据");

            // 将原始数据转换为SQL查询结果格式，以便使用现有的模板引擎
            SqlQueryResultVO sqlResult = convertToSqlResult(rawData);

            // 添加参数映射和字典转换处理
            List<InterfaceParamMappingEntity> paramMappings = interfaceParamMappingManager.getByInterfaceId(interfaceConfig.getId());
            if (paramMappings != null && !paramMappings.isEmpty() && sqlResult.getRows() != null) {
                log.info("任务调度：开始进行参数映射和字典转换，映射规则数量: {}", paramMappings.size());
                List<Map<String, Object>> mappedRows = new ArrayList<>(sqlResult.getRows().size());
                for (Map<String, Object> row : sqlResult.getRows()) {
                    Map<String, Object> mappedRow = paramMappingService.mapParameters(
                        row, paramMappings, interfaceConfig.getSystemCode(), true);
                    mappedRows.add(mappedRow);
                }
                sqlResult.setRows(mappedRows);
                log.info("任务调度：参数映射和字典转换完成，处理行数: {}", mappedRows.size());
            } else {
                log.info("任务调度：未配置参数映射规则，跳过参数映射和字典转换");
            }

            // 使用模板引擎处理入参模板
            String processedData = templateEngineService.processInputDataTemplate(
                interfaceConfig.getInputDataTemplate(),
                sqlResult
            );

            // 解析处理后的数据
            try {
                Object result = objectMapper.readValue(processedData, Object.class);
                log.info("任务调度：入参模板处理成功，数据类型: {}", result.getClass().getSimpleName());
                return result;
            } catch (Exception e) {
                log.warn("任务调度：模板结果不是有效JSON，返回字符串: {}", processedData);
                return processedData;
            }
        } catch (Exception e) {
            log.error("任务调度：数据组装失败，使用原始数据", e);
            return rawData;
        }
    }

    /**
     * 将原始数据转换为SQL查询结果格式
     *
     * @param rawData 原始数据
     * @return SQL查询结果
     */
    private SqlQueryResultVO convertToSqlResult(Object rawData) {
        SqlQueryResultVO sqlResult = new SqlQueryResultVO();

        if (rawData instanceof List) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> dataList = (List<Map<String, Object>>) rawData;

            if (!dataList.isEmpty()) {
                // 设置列名
                Set<String> allKeys = new LinkedHashSet<>();
                for (Map<String, Object> row : dataList) {
                    allKeys.addAll(row.keySet());
                }
                sqlResult.setColumns(new ArrayList<>(allKeys));

                // 设置行数据
                sqlResult.setRows(dataList);
            } else {
                sqlResult.setColumns(new ArrayList<>());
                sqlResult.setRows(new ArrayList<>());
            }
        } else if (rawData instanceof Map) {
            // 单行数据
            @SuppressWarnings("unchecked")
            Map<String, Object> singleRow = (Map<String, Object>) rawData;

            sqlResult.setColumns(new ArrayList<>(singleRow.keySet()));
            List<Map<String, Object>> rows = new ArrayList<>();
            rows.add(singleRow);
            sqlResult.setRows(rows);
        } else {
            // 其他类型数据，创建空结果
            sqlResult.setColumns(new ArrayList<>());
            sqlResult.setRows(new ArrayList<>());
        }

        return sqlResult;
    }

    /**
     * 构建上报数据摘要，避免大对象完整序列化带来的性能与日志压力
     * - 当为List时：仅采样前 sampleSize 条，并携带总数
     * - 其他对象：序列化后按 maxChars 限制截断
     */
    private String buildUploadDataSummary(Object data, int sampleSize, int maxChars) {
        if (data == null) {
            return null;
        }

        try {
            if (data instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> list = (List<Map<String, Object>>) data;
                int total = list.size();
                int sample = Math.min(sampleSize, total);

                Map<String, Object> summary = new HashMap<>();
                summary.put("totalCount", total);
                summary.put("sampleCount", sample);
                summary.put("sampleRows", sample == 0 ? new ArrayList<>() : list.subList(0, sample));

                String json = JSONObject.toJSONString(summary);
                return truncateText(json, maxChars);
            }

            // 非List情况，直接序列化并截断
            String json = JSONObject.toJSONString(data);
            return truncateText(json, maxChars);
        } catch (Exception e) {
            // 兜底：使用toString并截断
            return truncateText(String.valueOf(data), maxChars);
        }
    }

    /**
     * 创建或更新定时任务
     *
     * @param entity 任务实体
     */
    private void createOrUpdateJob(InterfaceTaskEntity entity) {
        // 创建SmartJob任务
        SmartJobAddForm jobAddForm = new SmartJobAddForm();
        jobAddForm.setJobName("接口任务-" + entity.getTaskName());
        jobAddForm.setJobClass("net.lingyue.ly.admin.autoRealy.interfaceTask.scheduler.InterfaceTaskExecutor");
        jobAddForm.setTriggerType(entity.getTriggerType());
        jobAddForm.setTriggerValue(entity.getTriggerValue());
        jobAddForm.setParam(String.valueOf(entity.getId()));
        jobAddForm.setEnabledFlag(entity.getEnabledFlag());
        jobAddForm.setRemark("接口任务调度：" + entity.getTaskName());
        jobAddForm.setSort(1);

        try {
            // 查询是否已存在相关任务
            String jobClass = jobAddForm.getJobClass();
            String jobParam = String.valueOf(entity.getId());
            ResponseDTO<Integer> existJobResult = interfaceJobService.getJobIdByClassAndParam(jobClass, jobParam);

            // 创建或更新任务
            if (existJobResult.getData() != null) {
                // 更新任务
                SmartJobUpdateForm updateForm = new SmartJobUpdateForm();
                BeanUtils.copyProperties(jobAddForm, updateForm);
                updateForm.setJobId(existJobResult.getData());
                ResponseDTO<String> updateResult = interfaceJobService.update(updateForm);
                if (!updateResult.getOk()) {
                    log.error("更新定时任务失败: {}", updateResult.getMsg());
                    throw new BusinessException("更新定时任务失败: " + updateResult.getMsg());
                }
                log.info("成功更新接口任务调度: {}", entity.getTaskName());
            } else {
                // 创建任务
                ResponseDTO<String> addResult = interfaceJobService.add(jobAddForm);
                if (!addResult.getOk()) {
                    log.error("创建定时任务失败: {}", addResult.getMsg());
                    throw new BusinessException("创建定时任务失败: " + addResult.getMsg());
                }
                log.info("成功创建接口任务调度: {}", entity.getTaskName());
            }
        } catch (Exception e) {
            log.error("创建或更新定时任务失败", e);
            throw new BusinessException("创建或更新定时任务失败: " + e.getMessage());
        }
    }

    /**
     * 移除定时任务
     *
     * @param taskId 任务ID
     */
    private void removeJob(Long taskId) {
        try {
            // 查询是否已存在相关任务
            String jobClass = "net.lingyue.ly.admin.autoRealy.interfaceTask.scheduler.InterfaceTaskExecutor";
            String jobParam = String.valueOf(taskId);
            ResponseDTO<Integer> existJobResult = interfaceJobService.getJobIdByClassAndParam(jobClass, jobParam);

            // 删除数据库中的任务记录
            if (existJobResult.getData() != null) {
                ResponseDTO<String> deleteResult = interfaceJobService.delete(existJobResult.getData());
                if (!deleteResult.getOk()) {
                    log.error("删除定时任务记录失败: {}", deleteResult.getMsg());
                    throw new BusinessException("删除定时任务记录失败: " + deleteResult.getMsg());
                }
                log.info("成功移除定时任务: taskId={}", taskId);
            }
        } catch (Exception e) {
            log.error("移除定时任务失败", e);
            throw new BusinessException("移除定时任务失败: " + e.getMessage());
        }
    }

    /**
     * 计算下次执行时间和推送相关时间信息
     *
     * @param taskVO 任务VO
     */
    private void calculateNextExecuteTime(InterfaceTaskVO taskVO) {
        if (!taskVO.getEnabledFlag()) {
            return;
        }

        try {
            // 查询任务
            String jobClass = "net.lingyue.ly.admin.autoRealy.interfaceTask.scheduler.InterfaceTaskExecutor";
            String jobParam = String.valueOf(taskVO.getId());
            ResponseDTO<Integer> existJobResult = interfaceJobService.getJobIdByClassAndParam(jobClass, jobParam);

            if (existJobResult.getData() != null) {
                // 获取下次执行时间
                ResponseDTO<List<LocalDateTime>> nextTimesResult = interfaceJobService.getNextExecuteTimes(existJobResult.getData(), 1);
                if (nextTimesResult.getData() != null && !nextTimesResult.getData().isEmpty()) {
                    taskVO.setNextExecuteTime(nextTimesResult.getData().get(0));
                }
            }

            // 设置推送相关时间信息
            setPushTimeInfo(taskVO);
        } catch (Exception e) {
            log.error("计算下次执行时间失败", e);
        }
    }

    /**
     * 设置推送相关时间信息
     */
    private void setPushTimeInfo(InterfaceTaskVO taskVO) {
        try {
            // 获取推送配置
            PushConfigEntity pushConfig = pushConfigDao.selectByTaskId(taskVO.getId());
            if (pushConfig != null) {
                // 设置推送起始时间
                taskVO.setPushStartTime(pushConfig.getStartTime());

                // 获取业务数据开始时间（断点续传的时间）
                LocalDateTime businessDataStartTime = getBusinessDataStartTime(taskVO.getId(), pushConfig);
                taskVO.setBusinessDataStartTime(businessDataStartTime);
            }
        } catch (Exception e) {
            log.error("设置推送时间信息失败，任务ID: {}", taskVO.getId(), e);
        }
    }

    /**
     * 获取业务数据开始时间（下次要推送的数据时间起点）
     */
    private LocalDateTime getBusinessDataStartTime(Long taskId, PushConfigEntity pushConfig) {
        try {
            // 从Redis获取断点信息
            String redisKey = "batch_processing:breakpoint:" + taskId;
            String breakpointJson = redisTemplate.opsForValue().get(redisKey);

            if (org.springframework.util.StringUtils.hasText(breakpointJson)) {
                // 解析断点信息
                ObjectMapper objectMapper = new ObjectMapper();
                objectMapper.registerModule(new JavaTimeModule());
                @SuppressWarnings("unchecked")
                Map<String, Object> breakpointMap = objectMapper.readValue(breakpointJson, Map.class);

                String lastTimeStr = (String) breakpointMap.get("lastTime");
                if (org.springframework.util.StringUtils.hasText(lastTimeStr)) {
                    return LocalDateTime.parse(lastTimeStr);
                }
            }

            // 如果没有断点信息，使用推送起始时间或上次执行时间
            if (pushConfig.getStartTime() != null) {
                return pushConfig.getStartTime();
            }

            // 最后使用任务的上次执行时间
            InterfaceTaskEntity task = interfaceTaskDao.selectById(taskId);
            if (task != null && task.getLastExecuteTime() != null) {
                return task.getLastExecuteTime();
            }

            // 默认使用前一天
            return LocalDateTime.now().minusDays(1);
        } catch (Exception e) {
            log.error("获取业务数据开始时间失败，任务ID: {}", taskId, e);
            return LocalDateTime.now().minusDays(1);
        }
    }

    /**
     * 校验Cron表达式是否有效
     *
     * @param cronExpression Cron表达式
     * @return 是否有效
     */
    private boolean isCronExpressionValid(String cronExpression) {
        try {
            return org.springframework.scheduling.support.CronExpression.isValidExpression(cronExpression);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 保存任务执行日志
     *
     * @param logVO 日志VO对象
     */
    private void saveTaskLog(InterfaceTaskLogVO logVO) {
        try {
            InterfaceTaskLogEntity logEntity = new InterfaceTaskLogEntity();
            logEntity.setTaskId(logVO.getTaskId());
            logEntity.setTaskName(logVO.getTaskName());
            logEntity.setSuccess(logVO.getSuccess());

            // 防止数据过大，进行安全截断
            logEntity.setExecuteResult(truncateText(logVO.getExecuteResult(), 1000000)); // 1MB限制
            logEntity.setRequestBody(truncateText(logVO.getRequestBody(), 1000000));     // 1MB限制
            logEntity.setResponseBody(truncateText(logVO.getResponseBody(), 1000000));   // 1MB限制

            logEntity.setStatusCode(logVO.getStatusCode());
            logEntity.setExecuteStartTime(logVO.getExecuteStartTime());
            logEntity.setExecuteEndTime(logVO.getExecuteEndTime());
            logEntity.setExecuteTimeMillis(logVO.getExecuteTimeMillis());
            logEntity.setCreateTime(LocalDateTime.now());

            interfaceTaskLogDao.insert(logEntity);
            log.info("已保存任务执行日志, taskId={}, success={}", logVO.getTaskId(), logVO.getSuccess());
        } catch (Exception e) {
            log.error("保存任务执行日志失败", e);
        }
    }

    /**
     * 安全截断文本，防止数据库字段长度超限
     *
     * @param text 原始文本
     * @param maxLength 最大长度
     * @return 截断后的文本
     */
    private String truncateText(String text, int maxLength) {
        if (text == null) {
            return null;
        }

        if (text.length() <= maxLength) {
            return text;
        }

        // 截断并添加提示信息
        String truncated = text.substring(0, maxLength - 100); // 预留100字符用于提示信息
        return truncated + "\n\n[数据过长已截断，原始长度: " + text.length() + " 字符，截断后长度: " + truncated.length() + " 字符]";
    }

    /**
     * 从复杂数据结构中提取数据行
     * 支持以下数据结构：
     * 1. List<Map<String, Object>> - 直接返回
     * 2. Map包含"rows"字段 - 提取rows字段
     * 3. Map包含"data"字段 - 提取data字段
     * 4. 其他复杂对象 - 尝试查找List类型的字段
     *
     * @param rawData 原始数据
     * @return 提取的数据行列表，如果无法提取则返回null
     */
    @SuppressWarnings({"unchecked", "unused"})
    private List<Map<String, Object>> extractDataRows(Object rawData) {
        if (rawData == null) {
            return null;
        }

        // 情况1：直接是List
        if (rawData instanceof List) {
            List<?> list = (List<?>) rawData;
            if (list.isEmpty()) {
                return new ArrayList<>();
            }

            // 检查List中的元素类型
            Object firstElement = list.get(0);
            if (firstElement instanceof Map) {
                return (List<Map<String, Object>>) rawData;
            }

            log.warn("List中的元素不是Map类型，无法处理: {}", firstElement.getClass().getSimpleName());
            return null;
        }

        // 情况2：是Map对象，尝试提取常见的数据字段
        if (rawData instanceof Map) {
            Map<String, Object> dataMap = (Map<String, Object>) rawData;

            // 尝试提取"rows"字段
            Object rows = dataMap.get("rows");
            if (rows instanceof List) {
                return extractDataRows(rows);
            }

            // 尝试提取"data"字段
            Object data = dataMap.get("data");
            if (data instanceof List) {
                return extractDataRows(data);
            }

            // 尝试提取"result"字段
            Object result = dataMap.get("result");
            if (result instanceof List) {
                return extractDataRows(result);
            }

            // 遍历Map的所有值，查找第一个List类型的字段
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                Object value = entry.getValue();
                if (value instanceof List) {
                    List<Map<String, Object>> extracted = extractDataRows(value);
                    if (extracted != null) {
                        log.info("从字段[{}]中提取到数据行: {}条", entry.getKey(), extracted.size());
                        return extracted;
                    }
                }
            }

            log.warn("Map对象中未找到可用的数据行字段: {}", dataMap.keySet());
            return null;
        }

        // 情况3：其他类型对象，尝试通过反射查找List字段
        try {
            Class<?> clazz = rawData.getClass();
            java.lang.reflect.Field[] fields = clazz.getDeclaredFields();

            for (java.lang.reflect.Field field : fields) {
                field.setAccessible(true);
                Object fieldValue = field.get(rawData);

                if (fieldValue instanceof List) {
                    List<Map<String, Object>> extracted = extractDataRows(fieldValue);
                    if (extracted != null) {
                        log.info("从对象字段[{}]中提取到数据行: {}条", field.getName(), extracted.size());
                        return extracted;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("通过反射提取数据行失败: {}", e.getMessage());
        }

        log.warn("无法从数据结构中提取数据行，数据类型: {}", rawData.getClass().getSimpleName());
        return null;
    }
}
